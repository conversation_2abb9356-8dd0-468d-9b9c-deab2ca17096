# 使用Python 3.11官方镜像作为基础镜像
FROM python:3.11-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# 复制requirements.txt并安装Python依赖
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 创建必要的目录
RUN mkdir -p /app/screenshots \
    && mkdir -p /app/temp_chrome_profile \
    && mkdir -p /app/driver

# 创建启动脚本
RUN echo '#!/bin/bash\n\
# 启动应用\n\
exec python main.py' > /app/start.sh \
    && chmod +x /app/start.sh

# 暴露端口
EXPOSE 8042

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8042/health || exit 1

# 启动应用
CMD ["/app/start.sh"]
